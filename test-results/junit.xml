<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="27" failures="0" errors="0" time="2.502">
  <testsuite name="Rate Limiter Middleware" errors="0" failures="0" skipped="0" timestamp="2025-08-12T06:25:08" time="0.849" tests="11">
    <testcase classname="Rate Limiter Middleware › createRateLimiter" name="should create a rate limiter with default configuration" time="0.023">
    </testcase>
    <testcase classname="Rate Limiter Middleware › createRateLimiter" name="should create a rate limiter with custom configuration" time="0.012">
    </testcase>
    <testcase classname="Rate Limiter Middleware › Rate limiting functionality" name="should allow requests within the limit" time="0.065">
    </testcase>
    <testcase classname="Rate Limiter Middleware › Rate limiting functionality" name="should block requests exceeding the limit" time="0.034">
    </testcase>
    <testcase classname="Rate Limiter Middleware › Rate limiting functionality" name="should include required headers in all responses" time="0.006">
    </testcase>
    <testcase classname="Rate Limiter Middleware › Health check endpoint exclusion" name="should skip rate limiting for health check endpoint" time="0.024">
    </testcase>
    <testcase classname="Rate Limiter Middleware › Pre-configured rate limiters" name="should have general rate limiter" time="0.001">
    </testcase>
    <testcase classname="Rate Limiter Middleware › Pre-configured rate limiters" name="should have auth rate limiter" time="0">
    </testcase>
    <testcase classname="Rate Limiter Middleware › Pre-configured rate limiters" name="should have public rate limiter" time="0.002">
    </testcase>
    <testcase classname="Rate Limiter Middleware › Environment configuration" name="should use environment variables for configuration" time="0.006">
    </testcase>
    <testcase classname="Rate Limiter Middleware › Custom key generator" name="should use IP address as default key" time="0.008">
    </testcase>
  </testsuite>
  <testsuite name="Rate Limiting Integration" errors="0" failures="0" skipped="0" timestamp="2025-08-12T06:25:08" time="1.437" tests="7">
    <testcase classname="Rate Limiting Integration › Health check endpoint" name="should not be rate limited" time="0.093">
    </testcase>
    <testcase classname="Rate Limiting Integration › General API rate limiting" name="should include rate limit headers in responses" time="0.005">
    </testcase>
    <testcase classname="Rate Limiting Integration › General API rate limiting" name="should return proper error format when rate limited" time="0.004">
    </testcase>
    <testcase classname="Rate Limiting Integration › Authentication endpoint rate limiting" name="should apply stricter rate limiting to auth endpoints" time="0.152">
    </testcase>
    <testcase classname="Rate Limiting Integration › Rate limit header format" name="should return headers in correct format" time="0.004">
    </testcase>
    <testcase classname="Rate Limiting Integration › Environment configuration" name="should respect environment variables" time="0.003">
    </testcase>
    <testcase classname="Rate Limiting Integration › CORS and rate limiting interaction" name="should work with CORS preflight requests" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="AuthController" errors="0" failures="0" skipped="0" timestamp="2025-08-12T06:25:08" time="1.734" tests="9">
    <testcase classname="AuthController › POST /api/v1/auth/login › successful login" name="should return 200 and auth token for valid credentials" time="0.148">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › successful login" name="should handle email case normalization" time="0.056">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › validation errors" name="should return 400 for missing email" time="0.044">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › validation errors" name="should return 400 for missing password" time="0.039">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › validation errors" name="should return 400 for invalid email format" time="0.038">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › authentication errors" name="should return 401 for non-existent user" time="0.036">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › authentication errors" name="should return 401 for invalid password" time="0.043">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › edge cases" name="should handle empty request body" time="0.035">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › edge cases" name="should handle malformed JSON" time="0.021">
    </testcase>
  </testsuite>
</testsuites>