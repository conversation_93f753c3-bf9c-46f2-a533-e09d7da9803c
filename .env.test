# Test Environment Configuration
NODE_ENV=test

# Server Configuration
PORT=3001
HOST=localhost

# JWT Configuration
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=1h

# Database Configuration (Test Database)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=paragon_api_test
DB_USER=postgres
DB_PASSWORD=postgres

# Bcrypt Configuration
SALT_ROUNDS=4

# CORS Configuration
CORS_ORIGIN=*

# Rate Limiting Configuration (Relaxed for testing)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_SKIP_SUCCESSFUL=false
RATE_LIMIT_SKIP_FAILED=false
RATE_LIMIT_SKIP_PATHS=

# Authentication Rate Limiting (Relaxed for testing)
RATE_LIMIT_AUTH_WINDOW_MS=60000
RATE_LIMIT_AUTH_MAX_REQUESTS=50

# Public Endpoints Rate Limiting (Relaxed for testing)
RATE_LIMIT_PUBLIC_WINDOW_MS=60000
RATE_LIMIT_PUBLIC_MAX_REQUESTS=2000
