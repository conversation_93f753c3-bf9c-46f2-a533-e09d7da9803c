# Server Configuration
PORT=3000
HOST=0.0.0.0

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=1h

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=paragon
DB_USER=postgres
DB_PASSWORD=postgres

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL=false
RATE_LIMIT_SKIP_FAILED=false
RATE_LIMIT_SKIP_PATHS=

# Authentication Rate Limiting
RATE_LIMIT_AUTH_WINDOW_MS=900000
RATE_LIMIT_AUTH_MAX_REQUESTS=5

# Public Endpoints Rate Limiting
RATE_LIMIT_PUBLIC_WINDOW_MS=900000
RATE_LIMIT_PUBLIC_MAX_REQUESTS=200